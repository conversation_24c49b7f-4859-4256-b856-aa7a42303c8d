import {
  createContext,
  useContext,
  useCallback,
  useState,
  type ReactNode,
} from "react";
import type { EventType, GameEvent } from "../models";

/**
 * ========================================================================
 * EventBusContext - Contexto para manejo de eventos en la aplicación
 *
 * Gestiona:
 * - Comunicación desacoplada entre componentes
 * - Eventos globales sin prop drilling
 * - Historial de eventos para debugging
 * - Suscripciones dinámicas con cleanup automático
 * ========================================================================
 */

export interface EventBusContextProps {
  emit: (type: EventType, payload?: any) => void;
  subscribe: (
    type: EventType,
    callback: (event: GameEvent) => void
  ) => () => void;
  getLastEvent: (type: EventType) => GameEvent | null;
}

const EventBusContext = createContext<EventBusContextProps | undefined>(
  undefined
);

export const useEventBus = () => {
  const context = useContext(EventBusContext);
  if (!context) {
    throw new Error("useEventBus must be used within EventBusProvider");
  }
  return context;
};

export const EventBusProvider = ({ children }: { children: ReactNode }) => {
  // ========== STATES ==========
  const [listeners, setListeners] = useState<
    Map<EventType, Set<(event: GameEvent) => void>>
  >(new Map());
  const [eventHistory, setEventHistory] = useState<GameEvent[]>([]);

  // ========== CALLBACKS ==========
  const emit = useCallback(
    (type: EventType, payload?: any) => {
      const event: GameEvent = {
        type,
        payload,
        timestamp: new Date(),
      };

      setEventHistory((prev) => [event, ...prev.slice(0, 99)]);

      const typeListeners = listeners.get(type);
      if (typeListeners) {
        typeListeners.forEach((callback) => {
          try {
            callback(event);
          } catch (error) {
            console.error(`Error in event listener for ${type}:`, error);
          }
        });
      }
    },
    [listeners]
  );

  const subscribe = useCallback(
    (type: EventType, callback: (event: GameEvent) => void) => {
      setListeners((prev) => {
        const newListeners = new Map(prev);
        if (!newListeners.has(type)) {
          newListeners.set(type, new Set());
        }
        newListeners.get(type)!.add(callback);
        return newListeners;
      });

      // Return unsubscribe function
      return () => {
        setListeners((prev) => {
          const newListeners = new Map(prev);
          const typeListeners = newListeners.get(type);
          if (typeListeners) {
            typeListeners.delete(callback);
            if (typeListeners.size === 0) {
              newListeners.delete(type);
            }
          }
          return newListeners;
        });
      };
    },
    []
  );

  const getLastEvent = useCallback(
    (type: EventType): GameEvent | null => {
      return eventHistory.find((event) => event.type === type) || null;
    },
    [eventHistory]
  );

  return (
    <EventBusContext.Provider value={{ emit, subscribe, getLastEvent }}>
      {children}
    </EventBusContext.Provider>
  );
};
