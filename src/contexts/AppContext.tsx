import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
  useMemo,
} from "react";
import { v4 as uuidv4 } from "uuid";

/**
 * ========================================================================
 * AppProvider - Contexto Principal de la Aplicación Enygma
 *
 * Gestiona:
 * - Estado global de la aplicación
 * - Configuración de las APIs
 * - Navegación entre vistas
 * - Manejo de errores
 * - Inicialización y reseteo de la aplicación
 * ========================================================================
 */

export type AppState =
  | "loading"        // Carga inicial de la aplicación
  | "initializing"   // Inicializando contextos y configuración
  | "consent"        // Esperando consentimiento de cookies
  | "welcome"        // Pantalla de bienvenida
  | "ready"          // Aplicación lista para usar
  | "error";         // Error en inicialización o funcionamiento

/**
 * Representación de un error capturado por la aplicación
 * Incluye contexto y metadatos para debugging
 */
export interface AppError {
  id: string;           // Identificador único del error
  message: string;      // Mensaje descriptivo del error
  timestamp: Date;      // Momento en que ocurrió el error
  context?: string;     // Contexto donde ocurrió (componente, función, etc.)
  error?: Error;        // Objeto Error original (si disponible)
}

/**
 * Configuración principal de la aplicación
 * Contiene todas las URLs y claves de APIs externas necesarias
 */
export interface AppConfig {
  iaApiUrl: string | null;        // URL de la API de IA
  iaApiKey: string | null;        // Clave de autenticación para IA
  speechApiUrl: string | null;    // URL de la API de síntesis de voz
  speechApiKey: string | null;    // Clave para servicio de speech
}

export interface AppContextProps {
  // Estado de la aplicación
  appState: AppState;
  isInitialized: boolean;

  // Configuración
  config: AppConfig;
  updateConfig: (newConfig: Partial<AppConfig>) => void;

  // Navegación
  currentView: string;
  navigate: (view: string) => void;

  // Inicialización
  initialize: () => Promise<void>;
  reset: () => void;

  // Manejo de errores
  errors: AppError[];
  addError: (error: AppError) => void;
  clearErrors: () => void;
  clearError: (errorId: string) => void;
}

const AppContext = createContext<AppContextProps | undefined>(undefined);

/**
 * Hook personalizado para acceder al contexto de aplicación
 * Incluye validación de uso dentro del Provider
 *
 * @returns {AppContextProps} Propiedades y métodos del contexto
 * @throws {Error} Si se usa fuera del AppProvider
 */
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

/**
 * Provider principal de la aplicación
 * Debe envolver toda la aplicación y ser el contexto más externo
 *
 * @param {Object} props - Props del componente
 * @param {ReactNode} props.children - Componentes hijos
 */
export const AppProvider = ({ children }: { children: ReactNode }) => {
  // ========== STATES ==========
  const [appState, setAppState] = useState<AppState>("initializing");
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [config, setConfig] = useState<AppConfig>(() => ({
    iaApiUrl: import.meta.env.VITE_IA_API_URL || null,
    iaApiKey: import.meta.env.VITE_IA_API_KEY || null,
    speechApiUrl: import.meta.env.VITE_SPEECH_API_URL || null,
    speechApiKey: import.meta.env.VITE_SPEECH_API_KEY || null,
  }));
  const [currentView, setCurrentView] = useState<string>("main");
  const [errors, setErrors] = useState<AppError[]>([]);

  // ========== EFFECTS ==========
  useEffect(() => {
    initialize();
  }, []);

  /**
   * Configuración de listeners globales para errores no manejados
   * Captura tanto errores de JavaScript como Promises rechazadas
   */
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      createAndAddError(
        `Error global: ${event.message}`,
        `${event.filename}:${event.lineno}:${event.colno}`,
        event.error
      );
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      createAndAddError(
        `Promise rechazada: ${event.reason}`,
        "Unhandled Promise Rejection",
        event.reason instanceof Error ? event.reason : undefined
      );
    };

    window.addEventListener("error", handleGlobalError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleGlobalError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, []);

  // ========== CALLBACKS ==========
  /**
   * Verificar si la configuración actual es válida para el funcionamiento
   * Valida que las APIs críticas estén configuradas
   */
  const isConfigValid = useCallback((): boolean => {
    return !!(
      config.iaApiUrl &&
      config.iaApiKey &&
      config.speechApiUrl &&
      config.speechApiKey
    );
  }, [config]);

  /**
   * Actualizar configuración parcialmente
   */
  const updateConfig = useCallback((newConfig: Partial<AppConfig>) => {
    setConfig((prev) => ({ ...prev, ...newConfig }));
  }, []);

  /**
   * Navegar a una vista específica
   */
  const navigate = useCallback((view: string) => {
    setCurrentView(view);
  }, []);

  /**
   * Agregar un error al registro de errores de la aplicación
   */
  const addError = useCallback((appError: AppError) => {
    setErrors((prev) => [...prev, appError]);
  }, []);

  /**
   * Función auxiliar para crear y agregar errores fácilmente
   */
  const createAndAddError = useCallback(
    (message: string, context?: string, error?: Error) => {
      const appError: AppError = {
        id: uuidv4(),
        message,
        timestamp: new Date(),
        context,
        error,
      };
      addError(appError);
    },
    [addError]
  );

  /**
   * Limpiar todos los errores registrados
   */
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  /**
   * Eliminar un error específico por su ID
   */
  const clearError = useCallback((id: string) => {
    setErrors((prev) => prev.filter((error) => error.id !== id));
  }, []);

  /**
   * Inicializar la aplicación
   * Valida la configuración y establece el estado correspondiente
   */
  const initialize = useCallback(async () => {
    try {
      setAppState("initializing");

      // Validar configuración crítica
      if (!isConfigValid()) {
        throw new Error(
          "Configuración incompleta: faltan variables de entorno críticas"
        );
      }

      setAppState("ready");
      setIsInitialized(true);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error desconocido";
      setAppState("error");
      createAndAddError(
        `Error en inicialización: ${errorMessage}`,
        "initialize",
        error instanceof Error ? error : undefined
      );
    }
  }, [isConfigValid, addError]);

  /**
   * Resetear completamente el estado de la aplicación
   * Limpia errores y reinicia el proceso de inicialización
   */
  const reset = useCallback(() => {
    setAppState("initializing");
    setIsInitialized(false);
    clearErrors();
    initialize();
  }, [initialize, clearErrors]);

  // ========== VALOR DEL CONTEXTO MEMOIZADO ==========
  const contextValue = useMemo<AppContextProps>(
    () => ({
      // Estado
      appState,
      isInitialized,

      // Configuración
      config,
      updateConfig,

      // Navegación
      currentView,
      navigate,

      // Métodos
      initialize,
      reset,

      // Errores
      errors,
      addError,
      clearErrors,
      clearError,
    }),
    [
      appState,
      isInitialized,
      config,
      updateConfig,
      currentView,
      navigate,
      initialize,
      reset,
      errors,
      addError,
      clearErrors,
      clearError,
    ]
  );

  return (
    <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>
  );
};
