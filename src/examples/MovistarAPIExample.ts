/**
 * ========================================================================
 * MOVISTAR API USAGE EXAMPLE
 * ========================================================================
 *
 * Ejemplo de cómo usar el servicio de API de Movistar Plus
 * para buscar contenido basado en personajes
 * ========================================================================
 */

import { movistarAPIService } from '../services/MovistarAPIService';
import { characterEnhancementService } from '../services/CharacterEnhancementService';
import { aiContentSpeechWrapper } from '../services/AIContentSpeechWrapper';
import type { MovistarContent } from '../models/services';

// ========== EJEMPLO BÁSICO ==========
export async function basicSearchExample() {
  console.log('🔍 Ejemplo básico de búsqueda en Movistar API');

  // Buscar contenido por personaje (usando un ejemplo que sabemos que funciona)
  const character = 'Simba';
  const content = await movistarAPIService.searchByCharacter(character);

  if (content) {
    console.log('✅ Contenido encontrado:', {
      id: content.id,
      title: content.title,
      description: content.description,
      type: content.type,
      year: content.year,
      lanzable: content.lanzable,
      image: content.image ? '✅ Tiene imagen' : '❌ Sin imagen'
    });

    // Mostrar información específica según el tipo
    if (content.type === 'content') {
      console.log('📺 Es contenido de Movistar Plus');
    } else if (content.type === 'person') {
      console.log('👤 Es una persona real');
    } else if (content.type === 'character') {
      console.log('🎭 Es un personaje');
    }
  } else {
    console.log('❌ No se encontró contenido para:', character);
  }
}

// ========== EJEMPLO CON MÚLTIPLES PERSONAJES ==========
export async function multipleCharactersExample() {
  console.log('🔍 Ejemplo con múltiples personajes');

  const characters = [
    'Simba',      // Sabemos que funciona
    'Batman',     // Personaje popular
    'Sinbad',     // Aparece en la respuesta de ejemplo
    'Superman',   // Otro personaje popular
    'Timbaland'   // Persona real que aparece en la respuesta
  ];

  const results: Array<{ character: string; content: MovistarContent | null }> = [];

  for (const character of characters) {
    console.log(`\n🔍 Buscando: ${character}`);

    try {
      const content = await movistarAPIService.searchByCharacter(character);
      results.push({ character, content });

      if (content) {
        console.log(`✅ Encontrado: "${content.title}" (${content.type})`);
        if (content.lanzable !== undefined) {
          console.log(`   📺 Lanzable: ${content.lanzable ? 'Sí' : 'No'}`);
        }
        if (content.image) {
          console.log(`   🖼️ Imagen disponible`);
        }
      } else {
        console.log(`❌ No encontrado`);
      }

      // Pausa para no saturar la API
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error(`❌ Error buscando ${character}:`, error);
      results.push({ character, content: null });
    }
  }

  // Resumen final
  console.log('\n📊 Resumen de búsquedas:');
  const found = results.filter(r => r.content !== null);
  const notFound = results.filter(r => r.content === null);

  console.log(`✅ Encontrados: ${found.length}/${results.length}`);
  console.log(`❌ No encontrados: ${notFound.length}/${results.length}`);

  if (found.length > 0) {
    console.log('\n🎬 Contenido encontrado:');
    found.forEach(({ character, content }) => {
      console.log(`- ${character}: "${content!.title}" (${content!.type})`);
    });
  }

  // Análisis por tipo
  const byType = found.reduce((acc, { content }) => {
    const type = content!.type || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (Object.keys(byType).length > 0) {
    console.log('\n📈 Distribución por tipo:');
    Object.entries(byType).forEach(([type, count]) => {
      console.log(`- ${type}: ${count}`);
    });
  }
}

// ========== EJEMPLO DE INTEGRACIÓN CON EL JUEGO ==========
export async function gameIntegrationExample(selectedCharacter: string) {
  console.log('🎮 Ejemplo de integración con el juego');

  try {
    // 1. Verificar que el servicio esté configurado
    if (!movistarAPIService.isConfigured()) {
      console.error('❌ Servicio de Movistar API no configurado');
      return null;
    }

    // 2. Buscar contenido relacionado con el personaje
    console.log(`🔍 Buscando contenido para: ${selectedCharacter}`);
    const content = await movistarAPIService.searchByCharacter(selectedCharacter);

    if (!content) {
      console.log(`ℹ️ No se encontró contenido específico para: ${selectedCharacter}`);
      return null;
    }

    // 3. Procesar el resultado para el juego
    const gameContent = {
      character: selectedCharacter,
      relatedContent: {
        title: content.title,
        description: content.description,
        type: content.type,
        year: content.year,
        image: content.image
      },
      hints: generateHintsFromContent(content),
      difficulty: calculateDifficulty(content)
    };

    console.log('✅ Contenido procesado para el juego:', gameContent);
    return gameContent;

  } catch (error) {
    console.error('❌ Error en integración con el juego:', error);
    return null;
  }
}

// ========== FUNCIONES AUXILIARES ==========
function generateHintsFromContent(content: MovistarContent): string[] {
  const hints: string[] = [];

  if (content.type) {
    hints.push(`Es de tipo: ${content.type}`);
  }

  if (content.year) {
    hints.push(`Fue lanzado en: ${content.year}`);
  }

  if (content.genre) {
    hints.push(`Pertenece al género: ${content.genre}`);
  }

  if (content.description) {
    // Extraer palabras clave de la descripción
    const keywords = extractKeywords(content.description);
    if (keywords.length > 0) {
      hints.push(`Palabras clave: ${keywords.join(', ')}`);
    }
  }

  return hints;
}

function calculateDifficulty(content: MovistarContent): 'easy' | 'medium' | 'hard' {
  let score = 0;

  // Factores que aumentan la dificultad
  if (content.year && content.year < 2000) score += 2;
  if (content.type === 'documentary') score += 1;
  if (content.genre === 'indie' || content.genre === 'arthouse') score += 2;

  // Factores que disminuyen la dificultad
  if (content.type === 'movie' && content.year && content.year > 2010) score -= 1;
  if (content.genre === 'action' || content.genre === 'comedy') score -= 1;

  if (score >= 2) return 'hard';
  if (score >= 1) return 'medium';
  return 'easy';
}

function extractKeywords(description: string): string[] {
  // Implementación simple de extracción de palabras clave
  const commonWords = ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'una', 'con', 'por', 'para', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'como', 'pero', 'sus', 'las', 'los'];

  return description
    .toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word))
    .slice(0, 3); // Solo las primeras 3 palabras clave
}

// ========== EJEMPLO DE HEALTH CHECK ==========
export async function healthCheckExample() {
  console.log('🏥 Verificando estado del servicio Movistar API');

  const isHealthy = await movistarAPIService.healthCheck();

  if (isHealthy) {
    console.log('✅ Servicio Movistar API funcionando correctamente');
  } else {
    console.log('❌ Servicio Movistar API no disponible');
  }

  // Mostrar configuración
  const config = movistarAPIService.getConfig();
  console.log('⚙️ Configuración actual:', config);
}

// ========== EJEMPLO DE MEJORA DE PERSONAJES ==========
export async function characterEnhancementExample() {
  console.log('🎭 Ejemplo de mejora de personajes con Movistar API');

  const testCharacters = ['Harry Potter', 'Batman', 'Sherlock Holmes'];

  for (const character of testCharacters) {
    console.log(`\n🔍 Mejorando: ${character}`);

    const enhanced = await characterEnhancementService.enhanceCharacter(character);

    console.log(`📊 Resultado:`, {
      name: enhanced.name,
      status: enhanced.enhancementStatus,
      category: enhanced.category,
      difficulty: enhanced.difficulty,
      hintsCount: enhanced.hints.length,
      hasMovistarContent: Boolean(enhanced.movistarContent)
    });

    if (enhanced.movistarContent) {
      console.log(`🎬 Contenido Movistar:`, {
        title: enhanced.movistarContent.title,
        type: enhanced.movistarContent.type,
        year: enhanced.movistarContent.year
      });
    }

    if (enhanced.hints.length > 0) {
      console.log(`💡 Pistas:`, enhanced.hints);
    }
  }
}

// ========== EJEMPLO DE FLUJO COMPLETO DEL JUEGO ==========
export async function completeGameFlowExample() {
  console.log('🎮 Ejemplo de flujo completo del juego con Movistar API');

  try {
    // 1. Generar y mejorar personaje
    console.log('🎭 Generando personaje mejorado...');
    const enhancedCharacter = await aiContentSpeechWrapper.generateEnhancedCharacter();

    if (!enhancedCharacter) {
      console.error('❌ No se pudo generar personaje mejorado');
      return;
    }

    console.log('✅ Personaje generado y mejorado:', {
      name: enhancedCharacter.name,
      status: enhancedCharacter.enhancementStatus,
      difficulty: enhancedCharacter.difficulty
    });

    // 2. Mostrar información del juego
    if (enhancedCharacter.movistarContent) {
      console.log('🎬 Información de Movistar Plus disponible para el final del juego');
    }

    // 3. Mostrar pistas disponibles
    if (enhancedCharacter.hints.length > 0) {
      console.log('💡 Pistas disponibles para el jugador:', enhancedCharacter.hints);
    }

    // 4. Simular final del juego con información adicional
    console.log('\n🏆 Final del juego - Información adicional:');
    if (enhancedCharacter.movistarContent) {
      console.log(`📺 "${enhancedCharacter.name}" aparece en: "${enhancedCharacter.movistarContent.title}"`);
      if (enhancedCharacter.movistarContent.description) {
        console.log(`📝 Descripción: ${enhancedCharacter.movistarContent.description}`);
      }
    }

  } catch (error) {
    console.error('❌ Error en flujo completo del juego:', error);
  }
}

// ========== EXPORTAR TODAS LAS FUNCIONES DE EJEMPLO ==========
export const movistarAPIExamples = {
  basicSearch: basicSearchExample,
  multipleCharacters: multipleCharactersExample,
  gameIntegration: gameIntegrationExample,
  healthCheck: healthCheckExample,
  characterEnhancement: characterEnhancementExample,
  completeGameFlow: completeGameFlowExample
};
