import type { ReactNode } from "react";
import type {
  GameResponseType,
} from "./game";

// ========== TIPOS DE EVENTOS ==========
export type EventType =
  | "game:start"
  | "game:end"
  | "speech:input"
  | "speech:output"
  | "ui:navigate"
  | "speech:output:enqueue"
  | "speech:output:cancel"
  | "speech:output:process";

export interface GameEvent {
  type: EventType;
  payload?: any;
  timestamp: Date;
}

// ========== CONTEXTO DE ENTRADA DE VOZ ==========
export interface SpeechInputState {
  isListening: boolean;
  isProcessing: boolean;
  confidence: number;
  lastValidatedResponse: GameResponseType | null;
  errorMessage: string | null;
}

// ========== PROPS COMUNES DE CONTEXTOS ==========
export interface ContextProviderProps {
  children: ReactNode;
}
