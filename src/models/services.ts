// ========== TIPOS DE SERVICIOS AI ==========
export interface AIResponse {
  ok: boolean;
  id: {
    ses: string;
    corr: string;
  };
  input: string;
  output: string;
  media?: MediaItem[];
  output_format?: string;
  sizes: {
    completion_tokens: number;
    prompt_tokens: number;
    total_tokens: number;
  };
  errcode?: string;
  message?: string;
  prompt_params?: any;
  model_params?: any;
  sequence?: any;
  session_params?: any;
  // Datos estructurados del juego (si están disponibles)
  gameData?: {
    respuesta: string;
    pista?: string;
    acertado: boolean;
    cuenta_regresiva?: number;
    juego_finalizado: boolean;
  };
}

export interface MediaItem {
  url?: string;
  data?: string;
  contenttype?: string;
  display?: 'inline' | 'link';
  title?: string;
  filename?: string;
  metadata?: {
    'page-number'?: number[];
    [key: string]: any;
  };
}

export interface APIPayload {
  id: {
    clt: string;
    ses?: string;
    corr?: string;
  };
  preset: string;
  query: string;
  query_args?: {
    query_id?: string;
    media?: MediaComponent | MediaComponent[];
    fields?: Record<string, any>;
  };
  prompt_params?: {
    preamble?: string | string[];
    examples?: string[];
    template?: string;
  };
  model_params: {
    max_tokens?: number;
    temperature?: number;
    top_p?: number;
  };
}

export interface MediaComponent {
  url?: string;
  data?: string;
}

export interface ResetResponse {
  ok: boolean;
  sesid: string;
  command: 'reset';
  preset: string;
  message?: string;
}

// ========== TIPOS DE NORMALIZACIÓN DE TEXTO ==========
export interface NormalizationOptions {
  removeAccents?: boolean;
  toLowerCase?: boolean;
  trimWhitespace?: boolean;
  removeSpecialChars?: boolean;
  maxLength?: number;
  preserveSpaces?: boolean;
}

export interface SanitizationOptions {
  maxLength?: number;
  removeHtmlChars?: boolean;
  removeScriptChars?: boolean;
  preserveBasicPunctuation?: boolean;
}

export interface SpeechSimulator {
  simulateInput: (text: string, delay?: number) => Promise<void>;
  simulateSequence: (texts: string[], delay?: number) => Promise<void>;
  simulateWithNoise: (text: string, noiseLevel?: number) => Promise<void>;
  simulateWithAccent: (text: string, accent?: 'andaluz' | 'mexicano' | 'argentino') => Promise<void>;
  simulateInterruption: (text: string, interruptAt?: number) => Promise<void>;
  simulateHesitation: (text: string) => Promise<void>;
  simulateBackground: (text: string, background?: 'music' | 'traffic' | 'crowd') => Promise<void>;
  simulateVolume: (text: string, volume?: 'whisper' | 'normal' | 'loud') => Promise<void>;
}

// ========== TIPOS DE MOVISTAR API ==========
export interface MovistarContent {
  id: number;
  title: string;
  description?: string;
  image?: string;
  type?: string;
  genre?: string;
  year?: number;
  duration?: number;
  rating?: string;
  url?: string;
  lanzable?: boolean;
}

export interface MovistarTag {
  id: string;
  type: string;
  name: string;
  links: Array<{
    rel: string;
    href: string;
    type: string;
  }>;
}

export interface MovistarContenido {
  DatosEditoriales: {
    Id: number;
    Titulo: string;
    Lanzable: boolean;
    Imagen?: string;
    Descripcion?: string;
    Genero?: string;
    Año?: number;
    Duracion?: number;
    Clasificacion?: string;
  };
}

export interface MovistarAPIResponse {
  self: {
    href: string;
    rel: string;
  };
  next: {
    href: string | null;
    rel: string;
  };
  prev: {
    href: string | null;
    rel: string;
  };
  tags: MovistarTag[];
  count: number;
  totalCount: number;
  Contenidos: MovistarContenido[];
}

export interface MovistarSearchOptions {
  mode?: string;
  profile?: string;
  start?: number;
  end?: number;
}
