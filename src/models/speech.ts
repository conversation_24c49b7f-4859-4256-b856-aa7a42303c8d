// ========== INTERFACES DE SERVICIOS DE VOZ ==========
export interface IVoicesService {
  getAvailableVoices(): Promise<string[]>;
  getAudio(text: string): Promise<Blob>;
  configVoice(genre: string): Promise<boolean>;
  getCurrentVoiceId(): string;
  getAvailableVoicesList(): string[];
  reset(): void;
  cleanupCache(): void;
  getPerformanceStats(): object;
}

// ========== PARÁMETROS Y CONFIGURACIÓN DE VOZ ==========
export interface VoiceParams {
  voice_id: string;
  rate: number;
  pitch: string;
  volume: string;
  style: string;
}

export interface AudioRequest {
  input_text: string;
  voice_params: VoiceParams;
  output_format: string;
}

// ========== ESTADÍSTICAS Y RENDIMIENTO ==========
export interface PerformanceStats {
  cache: {
    size: number;
    hitRate: number;
    ttl: number;
  };
  queue: {
    length: number;
    isProcessing: boolean;
  };
  voice: {
    configured: boolean;
    voiceId: string;
    availableCount: number;
  };
}

// ========== CACHE Y RETRY ==========
export interface CacheEntry {
  blob: Blob;
  timestamp: number;
  voiceId: string;
}

export interface RetryRequest<T> {
  (): Promise<T>;
}
