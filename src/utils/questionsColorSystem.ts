// Función para obtener la clase CSS basada en preguntas restantes
export const getQuestionsColorClass = (remainingQuestions: number): string => {
  if (remainingQuestions >= 16 && remainingQuestions <= 20) {
    return 'questions-default'; // Color por defecto (tu color actual)
  } else if (remainingQuestions >= 11 && remainingQuestions <= 15) {
    return 'questions-warning-light';
  } else if (remainingQuestions >= 6 && remainingQuestions <= 10) {
    return 'questions-warning-medium';
  } else if (remainingQuestions >= 1 && remainingQuestions <= 5) {
    return 'questions-warning-high';
  } else if (remainingQuestions === 0) {
    return 'questions-critical';
  }

  // Fallback para valores inesperados
  return 'questions-default';
};

// Función alternativa que devuelve directamente el color hex
export const getQuestionsColor = (remainingQuestions: number): string => {
  if (remainingQuestions >= 16 && remainingQuestions <= 20) {
    return '#88FFD5'; // Color por defecto (basado en tu CSS actual)
  } else if (remainingQuestions >= 11 && remainingQuestions <= 15) {
    return '#F6FFCB';
  } else if (remainingQuestions >= 6 && remainingQuestions <= 10) {
    return '#EBFF88';
  } else if (remainingQuestions >= 1 && remainingQuestions <= 5) {
    return '#FFD388';
  } else if (remainingQuestions === 0) {
    return '#FF4548';
  }

  return '#88FFD5'; // Fallback
};

// Hook personalizado para usar en React
import { useMemo } from 'react';

export const useQuestionsColor = (maxQuestions: number, questionCount: number) => {
  const remainingQuestions = maxQuestions - questionCount;

  const colorData = useMemo(() => ({
    remainingQuestions,
    colorClass: getQuestionsColorClass(remainingQuestions),
    hexColor: getQuestionsColor(remainingQuestions),
    isLow: remainingQuestions <= 5,
    isCritical: remainingQuestions === 0
  }), [remainingQuestions]);

  return colorData;
};
