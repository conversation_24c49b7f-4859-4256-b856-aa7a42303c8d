
@keyframes typing {
    0%, 20% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}
  
@keyframes aura-pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 30px rgba(136, 255, 213, 0.8);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
    }
}
  
@keyframes speech-pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
}

// Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Rules book
@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes pageFlipNext {
  0% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: perspective(1000px) rotateY(-90deg);
    opacity: 0.3;
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes pageFlipPrev {
  0% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0.3;
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes rulesImageCardFade {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes vibrateRotate {
  0%, 100% {
    transform: translateX(0) rotate(0deg);
  }
  10% {
    transform: translateX(-2px) rotate(-1deg);
  }
  20% {
    transform: translateX(2px) rotate(1deg);
  }
  30% {
    transform: translateX(-2px) rotate(-1deg);
  }
  40% {
    transform: translateX(2px) rotate(1deg);
  }
  50% {
    transform: translateX(0) rotate(0deg);
  }
}

