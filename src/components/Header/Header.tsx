import { useEffect, useState } from "react";
import { ControlButton, SafeAreaHome } from "microapps";
import { useSpeechOutput } from "../../contexts/SpeechProvider";
import IconGoBack from "../Buttons/GoBack/IconGoBack";
import IconMenu from "../Buttons/Menu/IconMenu";
import IconMicrophoneOn from "../Buttons/MicrophoneOn/IconMicrophoneOn";
import IconMicrophoneOff from "../Buttons/MicrophoneOff/IconMicrophoneOff";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isAzureTTSEnabled: azureFromState},
    pauseMusic,
    resumeMusic,
    toggleAzureTTS,
  } = useSpeechOutput();
  const [currentAzureTTSState, setCurrentAzureTTSState] = useState(azureFromState);

  useEffect(() => {
    setCurrentAzureTTSState(azureFromState);
  }, [azureFromState]);

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleMusicClick = () => {
    if (isMusicPlaying) {
      pauseMusic();
    } else {
      resumeMusic();
    }
  };

  const handleSpeechClick = () => {
    const newState = toggleAzureTTS();
    setCurrentAzureTTSState(newState);
  };

  return (
    <div className="header">
      <div className="header-left">
        {currentView === "main" && <IconMenu />}

        {showBackButton && currentView !== "main" && onBackToMain && (
          <div className="back-button" onClick={onBackToMain}>
            <IconGoBack />
          </div>
        )}
      </div>

      <div className="header-title">{renderTitle()}</div>

      <div className="header-right">
        <div>
          <ControlButton
            onClick={handleMusicClick}
            type="music"
            isActive={ !audioState.isMuted && isMusicPlaying}
            size="big"
          />

        </div>

        <div
          className={`sound-icon speech-control ${currentAzureTTSState ? "active" : "disabled"}`}
          onClick={handleSpeechClick}
          title={
            currentAzureTTSState
              ? "Azure TTS activado - Click para desactivar"
              : "Azure TTS desactivado - Click para activar"
          }
        >
          {currentAzureTTSState ? (
            <IconMicrophoneOn />
          ) : (
            <IconMicrophoneOff />
          )}
        </div>

        <div className="home-icon">
          <SafeAreaHome isVisible={false} />
        </div>
      </div>
    </div>
  );
};
