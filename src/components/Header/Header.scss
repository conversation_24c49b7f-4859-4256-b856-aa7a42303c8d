.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0rem 0rem 0rem 1rem;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 4;

  .header-title {
    font-weight: 600;
    font-style: SemiBold;
    font-size: 1.5rem;
    color: #88ffd5;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: -1;
    @media (max-width: 768px) {
      display: none;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
    @media (max-width: 768px) {
      gap: 1rem;
    }

    
  }
}
