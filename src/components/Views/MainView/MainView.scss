@use './../../../animations.scss';

// Main
.mainview {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100dvh;
  align-items: flex-start;
  padding: 0 200px;
  overflow: auto;

  @media (max-width: 768px) {
      padding: 0 2rem;
  }
  
  .rules-button {
      padding: 2rem;
      width: 200px;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 2;
      transition: all 0.2s;
      opacity: 0;
      cursor: pointer;
      &:hover {
          scale: 1.02;
      }
      .book-image {
        animation: vibrateRotate 1s ease-in-out infinite;

      }
  }

  .center {
      padding-top: 4rem;
      display: flex;
      align-items: center;
      flex-direction: column;
      flex-basis: 1;
      width: 100%;
      &-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 1rem;
          padding: 2rem 0;
          width: 100%;
          opacity: 0;

          .header-title {
              font-family: Playfair Display;
              font-style: SemiBold;
              font-size: 4rem;
              text-align: center;
              width: 100%;

          }
          .header-subtitle {
              width: 100%;
              text-align: center;
          }
      }

      &-modes {
          display: flex;
          flex-direction: row;
          gap: 2rem;
          padding-bottom: 2rem;
          .modes-card {
              display: flex;
              align-items: center;
              flex-direction: column;
              gap: 2rem;
              width: 100%;
              flex-wrap: wrap;
          }

          .modes-description {
              text-align: center;
          }
      }

  }
}

// Main Image
.image {
  &-enygma {
      border-radius: 100%;
      border: 2px solid #88ffd5;
      box-shadow: 0px 0px 12px 0px #88ffd5;
      opacity: 0;
  }
}

// Main button
.start-button {
  opacity: 0;

  @media (max-width: 768px) {
    margin-bottom: 11rem;
  }
}

.fade {
  opacity: 1;
  animation: fadeInUp 0.6s ease-out forwards;
}