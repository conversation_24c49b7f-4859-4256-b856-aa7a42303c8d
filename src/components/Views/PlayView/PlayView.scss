@use './../../../animations.scss';

// Main
.chat-view {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100dvh;
  align-items: flex-start;
  padding: 0 200px;
  overflow: auto;
  position: relative;
  width: 100%;

  .menu-left {
    position: absolute;
    left: 0;
    bottom: 0;
    .enygma-logo {
      position: relative;
      left: 0;
      bottom: 0;
      padding: 2rem;

      .enygma-image{
        border-radius: 138px;
        border: 2px solid #88FFD5;
        box-shadow: 0px 0px 12px 0px #88FFD5;
      }

      .speaking {
        animation: aura-pulse 2s infinite;
        border-radius: 61px;
        box-shadow: 0 0 20px rgba(136, 255, 213, 0.6);
        position: absolute;
        top: 0rem;
        right: 0rem;
      }

      .icon-aura {
        width: 90px;
        height: 90px;
        border-radius: 61px;
        padding: 16px;
        background: #88FFD5;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;


        &.speech-indicator {
          .speech-pulse {
            background: #40e0d0;
            color: #001428;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: speech-pulse 1.5s infinite;
            box-shadow: 0 2px 8px rgba(64, 224, 208, 0.4);
          }
        }
      }
    }
  }
}

// Conversation
.chat-view-wrapper {
  height: 100dvh;
  flex-basis: 1;
  display: flex;
  margin: 0 32px;
  width: 100%;
  flex-direction: column;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 12rem;
  width: 100%;
}

.chat-content {
  .chat-content {
    display: flex;

    &.align-right {
      justify-content: flex-end;
      text-align: right;
      color: #40e0d0;
    }

    &.align-left {
      justify-content: flex-start;
      text-align: left;
    }
  }
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  background: rgba(64, 224, 208, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(64, 224, 208, 0.3);

  p {
    margin: 0.5rem 0;
    line-height: 1.6;
  }
}

.chat-input-container {
  width: 100%;
  padding: 1rem 2rem;
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  width: 90%;
  margin: 0 auto;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(64, 224, 208, 0.1);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 25px;
  color: #e0e0e0;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    border-color: #40e0d0;
    background: rgba(64, 224, 208, 0.15);
    box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.2);
  }

  &::placeholder {
    color: #a0a0a0;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.send-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #40e0d0, #20b2aa);
  border: none;
  border-radius: 25px;
  color: #001428;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 224, 208, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

// Menu buttons
.menu-right {
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 1rem;

  .image-button {
    padding: 1rem;
    width: 150px;
    position: relative;
    left: 0;
    bottom: 0;
    z-index: 2;
    align-items: center;
    display: flex;
    flex-direction: column;
    //animation: fadeInUp 0.6s ease-out forwards;
        transition: all 0.2s;
        cursor: pointer;
        &:hover {
            scale: 1.02;
        }
  }
}

.chat-content {
  width: 100%;
  border: 2px solid #88FFD5;
  border-radius: 16px;
  box-shadow: 0px 0px 16px 0px #88FFD5;
  padding: 2.5rem;
  background-color: #001428;
  max-width: 600px;
  height: 250px;
  overflow: auto;
}

.questions-color-text {
  font-weight: bold;
  transition: color 0.3s ease;

  &.questions-default {
    color: #88FFD5;
  }

  &.questions-warning-light {
    color: #F6FFCB;
  }

  &.questions-warning-medium {
    color: #EBFF88;
  }

  &.questions-warning-high {
    color: #FFD388;
    // Opcional: añadir una pequeña animación de alerta
    animation: subtle-pulse 2s infinite;
  }

  &.questions-critical {
    color: #FF4548;
    animation: critical-pulse 1s infinite;
  }
}

// Opcional: versión para backgrounds si necesitas cambiar fondos también
.questions-bg {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;

  &.questions-default {
    background-color: rgba(136, 255, 213, 0.1);
    border-color: #88FFD5;
    box-shadow: 0px 0px 12px rgba(136, 255, 213, 0.3);
  }

  &.questions-warning-light {
    background-color: rgba(246, 255, 203, 0.1);
    border-color: #F6FFCB;
    box-shadow: 0px 0px 12px rgba(246, 255, 203, 0.3);
  }

  &.questions-warning-medium {
    background-color: rgba(235, 255, 136, 0.1);
    border-color: #EBFF88;
    box-shadow: 0px 0px 12px rgba(235, 255, 136, 0.3);
  }

  &.questions-warning-high {
    background-color: rgba(255, 211, 136, 0.1);
    border-color: #FFD388;
    box-shadow: 0px 0px 12px rgba(255, 211, 136, 0.3);
  }

  &.questions-critical {
    background-color: rgba(255, 69, 72, 0.1);
    border-color: #FF4548;
    box-shadow: 0px 0px 12px rgba(255, 69, 72, 0.4);
  }
}

// Animaciones para estados críticos
@keyframes subtle-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes critical-pulse {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(255, 69, 72, 0.5);
  }
  50% {
    opacity: 0.7;
    text-shadow: 0 0 10px rgba(255, 69, 72, 0.8);
  }
}
