import { useEffect, useState } from "react";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { PrimaryButton } from "microapps";
import "./GameResultView.scss";

interface GameResult {
  winner: "ai" | "user" | "draw";
  finalGuess?: string;
  wasCorrect?: boolean;
  currentCharacter?: string;
  questionsUsed: number;
  maxQuestions: number;
  mode: "ia_vs_player";
  endTime: Date;
}

interface GameResultViewProps {
  onPlayAgain: () => void;
  onBackToMain: () => void;
}

const GameResultView: React.FC<GameResultViewProps> = ({
  onPlayAgain,
  onBackToMain,
}) => {
  const { session } = useEnygmaGame();
  const [gameResult, setGameResult] = useState<GameResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasSpokenResult, setHasSpokenResult] = useState(false);

  useEffect(() => {
    const loadGameResult = () => {
      try {
        if (session && session.phase === "finished") {
          const result: GameResult = {
            currentCharacter: session.currentCharacter,
            endTime: session.endTime || new Date(),
            finalGuess: session.finalGuess,
            maxQuestions: session.maxQuestions,
            mode: session.mode,
            questionsUsed: session.questionCount,
            wasCorrect: session.wasCorrect,
            winner: session.winner || "draw",
          };
          setGameResult(result);

          // Guardar también en localStorage para persistencia
          localStorage.setItem(
            "enygma_last_game_result",
            JSON.stringify(result)
          );
        } else {
          const savedResult = localStorage.getItem("enygma_last_game_result");
          if (savedResult) {
            const parsed = JSON.parse(savedResult);
            parsed.endTime = new Date(parsed.endTime);
            setGameResult(parsed);
          }
        }
      } catch (error) {
        console.error("Error cargando resultado del juego:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGameResult();
  }, [session]);

  useEffect(() => {
    if (gameResult && !hasSpokenResult) {
      const narrateResult = async () => {
        try {
          setHasSpokenResult(true);
        } catch (error) {
          console.error("Error narrando resultado:", error);
          setHasSpokenResult(true);
        }
      };

      const timer = setTimeout(narrateResult, 1000);
      return () => clearTimeout(timer);
    }
  }, [gameResult, hasSpokenResult]);

  const getResultMessage = (): string => {
    if (!gameResult) return "";

    const { winner, currentCharacter } = gameResult;

    if (winner === "user") {
      return `El personaje es ${currentCharacter}.`;
    } else if (winner === "ai") {
      return `El personaje es ${currentCharacter}, pero no estabas tan lejos... ¿Quieres probar suerte con otro misterio?`;
    } else {
      return "¡Empate!";
    }
  };

  const getResultTitle = (): string => {
    if (!gameResult) return "";

    const { winner } = gameResult;

    if (winner === "user") {
      return "¡Has acertado!";
    } else if (winner === "ai") {
      return "¡Buen intento!";
    } else {
      return "¡Empate!";
    }
  };

  const handlePlayAgain = () => {
    localStorage.removeItem("enygma_last_game_result");
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("clues");
    setHasSpokenResult(false);
    onPlayAgain();
  };

  const handleBackToMain = () => {
    localStorage.removeItem("enygma_last_game_result");
    localStorage.removeItem("enygma_generated_character");
    localStorage.removeItem("clues");
    setHasSpokenResult(false);
    onBackToMain();
  };

  if (isLoading) {
    return (
      <div className="content game-result-modal">
        <div className="result-container loading">
          <div className="loading-spinner">
            <div className="spinner"></div>
          </div>
          <p>Analizando los resultados...</p>
        </div>
      </div>
    );
  }

  if (!gameResult) {
    return (
      <div className="content game-result-modal">
        <div className="result-container error">
          <h2>Error</h2>

          <p>No se pudieron cargar los resultados del juego.</p>

          <PrimaryButton
            onClick={handleBackToMain}
            text="Volver al menú"
            backgroundColor="#88FFD5"
            textColor="#001428"
          />
        </div>
      </div>
    );
  }

  return (
    <div className="content game-result-modal">
      <div className="result-container">
        <h1 className="result-title">
          {getResultTitle()}
        </h1>

        {/* Mensaje descriptivo */}
        <div className="result-message">
          <p>{getResultMessage()}</p>
        </div>

        {/* Botones de acción */}
        <div className="result-actions">
          <PrimaryButton
            onClick={handlePlayAgain}
            text="Jugar de nuevo"
            backgroundColor="#88FFD5"
            textColor="#001428"
            borderRadius="8px"
            spinnerColor="#000"
          />

          {/* <button onClick={handleBackToMain} className="secondary-button">
            Volver al menú
          </button> */}
        </div>
      </div>

      <div className="result-films">
        Resultados películas
      </div>
    </div>
  );
};

export default GameResultView;
