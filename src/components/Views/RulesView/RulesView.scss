@use './../../../animations.scss';

// Main
.rules-modal {
  animation: fadeInUp 0.6s ease-out forwards;
  display: flex;
  width: 100%;
  padding-top: 10rem;
  flex-direction: column;
  justify-content: center;
  overflow: auto;
  // background-color: red;
  padding-bottom: 4rem;

  @media (max-width: 768px) {
    padding-top: 6rem;
  }

  @media (max-width: 680px) {
    height: 100dvh;
  }

  .rules-content {
    gap: 4rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .rules-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .rules-book {
    display: flex;
    gap: 1rem;
    // background-color: green;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 450px;

    @media (max-width: 768px) {
      height: 350px;
    }

    @media (max-width: 680px) {
      height: auto;
    }
  }
}

// Page Flip Animations
.rules-page-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  // background-color: yellow;
  gap: 1rem;
  height: 100%;
  justify-content: center;

  @media (max-width: 680px) {
    flex-direction: column;
    width: 100%;
    gap: 1rem;
    height: 100%;
  }

  &.page-flip-next {
    .rules-text-card {
      animation: pageFlipNext 0.6s ease-in-out;
    }
  }

  &.page-flip-prev {
    .rules-text-card {
      animation: pageFlipPrev 0.6s ease-in-out;
    }
  }
}

// Card
.rules-card {
  width: 300px;
  background: linear-gradient(135deg, #002332 0%, #001a26 100%);
  border: 2px solid #88ffd5;
  box-shadow: 0px 0px 12px 0px #88ffd5;
  border-radius: 16px;
  height: 450px;
  position: relative;
  display: flex;

  @media (max-width: 768px) {
    width: 250px;
    height: 350px;
  }

  @media (max-width: 680px) {
    width: 100%;
    height: 300px;
  }
}

// Image Card
.rules-image-card {
  overflow: hidden;


  &.fade-animation {
    animation: rulesImageCardFade 0.6s ease-in-out;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Text Card
.rules-text-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @media (max-width: 680px) {
    height: 100%;
  }

  .rules-card-text {
    margin: 3rem;
    color: #88ffd5;
    text-align: center;
    display: flex;
    align-items: center;
    height: 100%;

    @media (max-width: 768px) {
      margin: 2rem
    }
  }

  .rules-page-indicators {
    margin: 3rem;
    display: flex;
    justify-content: center;
    gap: 8px;

    .rules-page-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(74, 222, 128, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #88ffd5;
        transform: scale(1.5);
      }

      &:hover:not(.active) {
        background: rgba(74, 222, 128, 0.6);
        transform: scale(1.2);
      }
    }
  }
}

// Navigation Arrows
.rules-nav-arrow {
  background: rgba(11, 39, 57, 0.9);
  border: 2px solid #88ffd5;
  box-shadow: 0px 0px 12px 0px #88ffd5;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  @media (max-width: 680px) {
    position: absolute;
    bottom: 1rem;
    left: 0;
    right: 0;
    margin: auto;
  }
}