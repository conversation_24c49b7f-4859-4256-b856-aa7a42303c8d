import { useEffect, useState } from "react";
import { Tag } from "microapps";
import "./CluesView.scss";

interface Clue {
  id: string;
  text: string;
  sessionId: string;
  timestamp: string;
}

interface CluesData {
  clues: Clue[];
  lastUpdated: string;
  sessionId: string;
}

const CluesView: React.FC = ({}) => {
  const [clues, setClues] = useState<Clue[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCluesFromLocalStorage();
  }, []);

  const loadCluesFromLocalStorage = () => {
    try {
      const storedData = localStorage.getItem("clues");
      if (storedData) {
        const cluesData: CluesData = JSON.parse(storedData);
        setClues(cluesData.clues || []);
      }
    } catch (error) {
      console.error("Error al cargar las pistas desde localStorage:", error);
      setClues([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="content clues-modal">
        <div className="clues-wrapper">
          <div className="game-header">
            <h1>Pistas descubiertas</h1>
          </div>
          <div className="clues-container">
            <div>Cargando pistas...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="content clues-modal">
      <div className="clues-wrapper">
        <div className="clues-container">
          {clues.length === 0 ? (
            <div className="no-clues-message">
              Aquí aparecerán las pistas que vayas descubriendo... Por ahora, los
              secretos permanecen ocultos en las sombras. Hazme tu primera
              pregunta y comenzaré a revelarlos.
            </div>
          ) : (
            <div className="clues-list">
              {clues.map((clue) => (
                <div key={clue.id} className="clue-item">
                  <Tag
                    text={clue.text}
                    type="active"
                    backgroundColor="#D6FDF1"
                    textColor="#000"
                    className="clue-tag"
                    id={clue.id}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CluesView;
