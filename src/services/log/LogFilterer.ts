import type { LogEntry, LogFilter } from "./LogTypes";

export class LogFilterer {
  static filter<T>(logs: LogEntry<T>[], filter: LogFilter): LogEntry<T>[] {
    return logs.filter((entry) => {
      if (filter.levels && !filter.levels.includes(entry.level)) return false;
      if (filter.services && !filter.services.includes(entry.service))
        return false;
      if (
        filter.text &&
        !entry.message.includes(filter.text) &&
        JSON.stringify(entry.data).indexOf(filter.text) < 0
      )
        return false;
      if (filter.dateFrom && entry.timestamp < filter.dateFrom) return false;
      if (filter.dateTo && entry.timestamp > filter.dateTo) return false;
      return true;
    });
  }
}
