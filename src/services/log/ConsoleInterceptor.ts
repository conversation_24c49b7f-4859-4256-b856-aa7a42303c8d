import { rawConsole } from "./RawConsole";
import { LogService } from "./LogService";

export class ConsoleInterceptor {
  private logService: LogService;

  constructor(logService: LogService) {
    this.logService = logService;
  }

  start() {
    console.log = (...args: unknown[]) => {
      this.logService.debug("Console", args.join(" "));
      rawConsole.log(...args);
    };
    console.info = (...args: unknown[]) => {
      this.logService.info("Console", args.join(" "));
      rawConsole.info(...args);
    };
    console.warn = (...args: unknown[]) => {
      this.logService.warn("Console", args.join(" "));
      rawConsole.warn(...args);
    };
    console.error = (...args: unknown[]) => {
      this.logService.error("Console", args.join(" "));
      rawConsole.error(...args);
    };
  }

  stop() {
    console.log = rawConsole.log;
    console.info = rawConsole.info;
    console.warn = rawConsole.warn;
    console.error = rawConsole.error;
  }
}
