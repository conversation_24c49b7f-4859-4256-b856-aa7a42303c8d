export type Listener<T = unknown> = (logs: LogEntry<T>[]) => void;

export type LogLevel = "debug" | "info" | "warn" | "error" | "success";

export interface LogEntry<T = unknown> {
  id: string;
  timestamp: Date;
  service: string;
  level: LogLevel;
  message: string;
  data?: T;
  stack?: string;
}

export interface LogFilter {
  levels?: LogLevel[];
  services?: string[];
  text?: string;
  dateFrom?: Date;
  dateTo?: Date;
}
