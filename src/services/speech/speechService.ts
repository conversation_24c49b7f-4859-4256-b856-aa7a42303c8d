import { AzureVoicesService } from "./azureVoicesService";

/**
 * Servicio principal de Speech - Maneja la generación de audio desde texto
 * Solo se encarga de la comunicación con Azure TTS, no de la reproducción
 */
export class SpeechService {
  private serviceName = "speechService";
  private azureService: AzureVoicesService;
  private isAzureEnabled: boolean = true;
  private readonly AZURE_TTS_STORAGE_KEY = "azure-tts-enabled";
  private listeners: Set<(enabled: boolean) => void> = new Set();

  // ========== CONSTRUCTOR E INICIALIZACIÓN ==========
  constructor() {
    this.azureService = AzureVoicesService.getInstance();
    this.loadAzureTTSState();
  }

  // ========== SISTEMA DE LISTENERS ==========
  public addListener(callback: (enabled: boolean) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  private notifyListeners(): void {
    this.listeners.forEach((callback) => {
      try {
        callback(this.isAzureEnabled);
      } catch (error) {
        console.error(`❌ [${this.serviceName}] Error en listener:`, error);
      }
    });
  }

  // ========== GETTERS Y ESTADO ==========

  public getCurrentVoiceId(): string {
    return this.azureService.getCurrentVoiceId();
  }

  public getAvailableVoicesList(): string[] {
    return this.azureService.getAvailableVoicesList();
  }

  // ========== CONTROL DE ACTIVACIÓN DE AZURE TTS ==========
  public enableAzureTTS(): void {
    if (this.isAzureEnabled) return;
    this.isAzureEnabled = true;
    this.saveAzureTTSState();
    this.notifyListeners();
    // const verification = this.isAzureTTSEnabled();
  }

  public disableAzureTTS(): void {
    if (!this.isAzureEnabled) return;
    this.isAzureEnabled = false;
    this.saveAzureTTSState();
    this.notifyListeners();
    // const verification = this.isAzureTTSEnabled();
  }

  public toggleAzureTTS(): boolean {
    const oldState = this.isAzureEnabled;

    this.isAzureEnabled = !this.isAzureEnabled;
    this.saveAzureTTSState();

    if (oldState !== this.isAzureEnabled) {
      this.notifyListeners();
    }

    const verification = this.isAzureTTSEnabled();

    return verification;
  }

  public isAzureTTSEnabled(): boolean {
    try {
      const saved = localStorage.getItem(this.AZURE_TTS_STORAGE_KEY);
      if (saved !== null) {
        const storageValue = JSON.parse(saved);

        // Si hay diferencia con el estado en memoria, actualizar
        if (this.isAzureEnabled !== storageValue) {
          this.isAzureEnabled = storageValue;
        }

        return this.isAzureEnabled;
      }
    } catch (error) {
      console.warn(`⚠️ [speechService] Error leyendo localStorage:`, error);
    }

    return this.isAzureEnabled;
  }

  public forceDisableAzureTTS(): void {
    this.isAzureEnabled = false;
    this.saveAzureTTSState();
  }

  public syncFromStorage(): boolean {
    const oldState = this.isAzureEnabled;
    this.loadAzureTTSState();

    if (oldState !== this.isAzureEnabled) {
      this.notifyListeners();
    }

    return this.isAzureEnabled;
  }

  // ========== PERSISTENCIA DE ESTADO ==========
  private loadAzureTTSState(): void {
    try {
      const saved = localStorage.getItem(this.AZURE_TTS_STORAGE_KEY);
      if (saved !== null) {
        this.isAzureEnabled = JSON.parse(saved);
      } else {
        // Si no hay valor guardado, usar true como defecto y guardarlo
        this.isAzureEnabled = true;
        this.saveAzureTTSState();
      }
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error cargando estado:`, error);
      this.isAzureEnabled = true;
    }
  }

  private saveAzureTTSState(): void {
    try {
      localStorage.setItem(
        this.AZURE_TTS_STORAGE_KEY,
        JSON.stringify(this.isAzureEnabled)
      );
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error guardando estado:`, error);
    }
  }

  // ========== CONFIGURACIÓN DE VOZ ==========
  public async configVoice(genre: string): Promise<boolean> {
    try {
      const success = await this.azureService.configVoice(genre);
      return success;
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error configurando voz`, error);
      return false;
    }
  }

  // ========== API PRINCIPAL DE GENERACIÓN ==========
  public async getAudio(message: string): Promise<Blob> {
    if (!this.isAzureEnabled) {
      throw new Error("Azure TTS está desactivado");
    }

    try {
      const audioBlob = await this.azureService.getAudio(message);
      return audioBlob;
    } catch (error) {
      console.error(`❌ [speechService] Error obteniendo audio`, error);
      throw error;
    }
  }

  /**
   * @deprecated Usar getAudio() directamente y AudioManager.playSpeech() para reproducir
   * Este método se mantiene temporalmente para compatibilidad
   */
  public async getSpeech(message: string): Promise<string> {
    try {
      const audioBlob = await this.getAudio(message);
      const audioUrl = URL.createObjectURL(audioBlob);
      return audioUrl;
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error obteniendo URL de speech`,
        error
      );
      throw error;
    }
  }

  // ========== MÉTODOS DE CONVENIENCIA PARA GENERACIÓN ==========

  /**
   * @deprecated Usar getAudio() directamente y AudioManager para reproducir
   * Este método se mantiene temporalmente para compatibilidad
   */
  public async speak(_text: string): Promise<void> {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ speak() está deprecated, usar getAudio() + AudioManager.playSpeech()`
    );
    throw new Error(
      "speak() está deprecated. Usar getAudio() + AudioManager.playSpeech()"
    );
  }

  /**
   * @deprecated Usar getAudio() directamente y AudioManager para reproducir
   */
  public async toSpeech(_url: string): Promise<void> {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ toSpeech() está deprecated, usar AudioManager`
    );
    throw new Error(
      "toSpeech() está deprecated. Usar AudioManager.playSpeech()"
    );
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public setSpeech(_url: string): void {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ setSpeech() está deprecated, usar AudioManager`
    );
    throw new Error("setSpeech() está deprecated. Usar AudioManager");
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public async playSpeech(): Promise<void> {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ playSpeech() está deprecated, usar AudioManager`
    );
    throw new Error(
      "playSpeech() está deprecated. Usar AudioManager.playSpeech()"
    );
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public stopSpeech(): void {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ stopSpeech() está deprecated, usar AudioManager`
    );
    throw new Error(
      "stopSpeech() está deprecated. Usar AudioManager.stopSpeech()"
    );
  }

  /**
   * @deprecated Usar AudioManager para control de reproducción
   */
  public noSpeech(): void {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ noSpeech() está deprecated, usar AudioManager`
    );
    throw new Error(
      "noSpeech() está deprecated. Usar AudioManager.stopSpeech()"
    );
  }

  // ========== AUTO-CONFIGURACIÓN Y GENERACIÓN ==========
  public async generateAudioWithAutoConfig(
    text: string,
    genre: string = "female"
  ): Promise<Blob> {
    if (!this.isAzureEnabled) {
      throw new Error("Azure TTS está desactivado");
    }

    try {
      if (!this.getCurrentVoiceId()) {
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      return await this.getAudio(text);
    } catch (error) {
      console.error(
        `❌ [${this.serviceName}] Error en generateAudioWithAutoConfig`,
        error
      );
      throw error;
    }
  }

  /**
   * @deprecated Usar generateAudioWithAutoConfig() + AudioManager.playSpeech()
   */
  public async speakWithAutoConfig(
    _text: string,
    _genre: string = "female"
  ): Promise<void> {
    console.warn(
      `⚠️ [${this.serviceName}] ⚠️ speakWithAutoConfig() está deprecated, usar generateAudioWithAutoConfig() + AudioManager`
    );
    throw new Error(
      "speakWithAutoConfig() está deprecated. Usar generateAudioWithAutoConfig() + AudioManager.playSpeech()"
    );
  }

  // ========== UTILIDADES Y LIMPIEZA ==========
  public cleanup(): void {
    this.azureService.cleanupCache();
  }
}

// Exportar instancia singleton
export const speechService = new SpeechService();
