/**
 * ========================================================================
 * API CONFIGURATION
 * ========================================================================
 *
 * Configuración centralizada para la API de IA
 * Maneja variables de entorno y validaciones
 * ========================================================================
 */

export interface APIPresets {
  aura: string;
  user: string;
  genCharBot: string;
}

export interface APIConfiguration {
  baseURL: string;
  apiKey: string;
  presets: APIPresets;
}

export interface ConfigValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ========== CONFIGURACIÓN PRINCIPAL ==========
export const API_CONFIG: APIConfiguration = {
  baseURL:
    import.meta.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm/",
  apiKey:
    import.meta.env.VITE_IA_API_KEY || "b3df20aa-db3b-49ef-8d3b-4abfac8c1161",
  presets: {
    aura:
      import.meta.env.VITE_IA_PRESETID_IA_VS_PLAYER ||
      "mapp-Claude - Enygma V1",
    user:
      import.meta.env.VITE_IA_PRESETID_USER ||
      "mapp-user-default",
    genCharBot:
      import.meta.env.VITE_IA_PRESETID_GENCHARBOT || "mapp-gen-char-bot",
  },
};

// ========== VALIDACIÓN DE CONFIGURACIÓN ==========
export function validateConfig(): ConfigValidation {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validar URL base
  if (!API_CONFIG.baseURL) {
    errors.push("API baseURL no configurada");
  } else if (!isValidUrl(API_CONFIG.baseURL)) {
    errors.push("API baseURL no es una URL válida");
  }

  // Validar API Key
  if (!API_CONFIG.apiKey) {
    errors.push("API Key no configurada");
  } else if (API_CONFIG.apiKey.length < 10) {
    warnings.push("API Key parece ser muy corta");
  }

  // Validar presets
  Object.entries(API_CONFIG.presets).forEach(([key, value]) => {
    if (!value) {
      errors.push(`Preset '${key}' no configurado`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// ========== HELPERS ==========
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// ========== GETTERS SEGUROS ==========
export function getAPIConfig(): APIConfiguration {
  return { ...API_CONFIG };
}

export function getPreset(type: keyof APIPresets): string {
  return API_CONFIG.presets[type];
}

export function isConfigValid(): boolean {
  return validateConfig().isValid;
}
