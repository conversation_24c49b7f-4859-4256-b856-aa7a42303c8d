/**
 * ========================================================================
 * CHARACTER STORAGE
 * ========================================================================
 *
 * Maneja el almacenamiento y gestión de personajes generados
 * Incluye persistencia, historial y validación
 * ========================================================================
 */

export interface GeneratedCharacter {
  id: string;
  name: string;
  generatedAt: Date;
  sessionId?: string;
  used: boolean;
  category?: 'real' | 'fictional' | 'historical' | 'unknown';
  source?: string; // De dónde se generó (ai, manual, etc.)
}

export interface CharacterHistory {
  characters: GeneratedCharacter[];
  lastGenerated: Date | null;
  totalGenerated: number;
  currentCharacter: GeneratedCharacter | null;
}

export class CharacterStorage {
  private serviceName = 'characterStorage';
  private storageKey = 'enygma_generated_character';

  // ========== GESTIÓN DEL PERSONAJE ACTUAL ==========

  /**
   * Guarda el personaje actual en localStorage
   */
  public saveCurrentCharacter(character: string, sessionId?: string): GeneratedCharacter {
    try {
      const characterData: GeneratedCharacter = {
        id: this.generateCharacterId(),
        name: character.trim(),
        generatedAt: new Date(),
        sessionId,
        used: false,
        source: 'ai'
      };

      // Ofuscar el personaje para que el usuario no adivine quien es
      const obfuscatedCharacter = this.obfuscateCharacter(character);

      // Guardar en la clave simple para compatibilidad (ahora ofuscado)
      localStorage.setItem(this.storageKey, obfuscatedCharacter);

      return characterData;

    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error guardando personaje:`, error);
      throw error;
    }
  }

  /**
   * Obtiene el personaje actual desde localStorage
   */
  public getCurrentCharacter(): string | null {
    try {
      const obfuscatedCharacter = localStorage.getItem(this.storageKey);
      if (!obfuscatedCharacter) {
        return null;
      }

      // Deofuscar el personaje antes de devolverlo
      return this.deobfuscateCharacter(obfuscatedCharacter);
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error leyendo personaje actual:`, error);
      return null;
    }
  }

  /**
   * Elimina el personaje actual
   */
  public clearCurrentCharacter(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error eliminando personaje:`, error);
    }
  }

  /**
   * Verifica si hay un personaje actual guardado
   */
  public hasCurrentCharacter(): boolean {
    return this.getCurrentCharacter() !== null;
  }

  // ========== MÉTODOS PRIVADOS ==========

  private generateCharacterId(): string {
    return `char-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  }

  /**
   * Ofusca el nombre del personaje para evitar que el usuario lo vea en localStorage
   */
  private obfuscateCharacter(character: string): string {
    try {
      console.log("El personaje antes de ofuscar es:", character);
      // Usar una clave simple basada en el timestamp y una constante
      const key = 'enygma_char_key_2024';
      const timestamp = Date.now().toString();

      // Combinar el personaje con datos adicionales para ofuscar
      const dataToEncode = JSON.stringify({
        c: character,
        t: timestamp,
        k: key.split('').reverse().join(''), // Invertir la clave
        r: Math.random().toString(36).substring(2, 10) // Ruido aleatorio
      });

      // Codificar en Base64 usando TextEncoder (método moderno)
      const encoder = new TextEncoder();
      const data = encoder.encode(dataToEncode);
      const encoded = btoa(String.fromCharCode(...data));
      return `enygma_obf_${encoded}`;

    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error ofuscando personaje, guardando sin ofuscar:`, error);
      return character;
    }
  }

  /**
   * Deofusca el nombre del personaje desde localStorage
   */
  private deobfuscateCharacter(obfuscatedData: string): string {
    try {
      // Verificar si los datos están ofuscados
      if (!obfuscatedData.startsWith('enygma_obf_')) {
        // Si no están ofuscados, devolverlos tal como están (compatibilidad hacia atrás)
        return obfuscatedData;
      }

      // Remover el prefijo y decodificar
      const encoded = obfuscatedData.replace('enygma_obf_', '');
      const binaryString = atob(encoded);

      // Convertir de binary string a Uint8Array y luego decodificar
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const decoder = new TextDecoder();
      const decoded = decoder.decode(bytes);
      const parsedData = JSON.parse(decoded);

      // Validar que la estructura sea correcta
      if (parsedData && typeof parsedData.c === 'string') {
        return parsedData.c;
      }

      throw new Error('Estructura de datos inválida');

    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error deofuscando personaje:`, error);
      // Si hay error, intentar devolver los datos tal como están
      return obfuscatedData.replace('enygma_obf_', '');
    }
  }

  /**
   * Valida si un nombre de personaje es válido
   */
  public isValidCharacterName(name: string): boolean {
    if (!name || typeof name !== 'string') return false;

    const trimmed = name.trim();
    return trimmed.length >= 2 && trimmed.length <= 100;
  }

  /**
   * Normaliza el nombre del personaje
   */
  public normalizeCharacterName(name: string): string {
    return name
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s\-'\.]/g, '')
      .substring(0, 100);
  }
}

// ========== SINGLETON EXPORT ==========
export const characterStorage = new CharacterStorage();
