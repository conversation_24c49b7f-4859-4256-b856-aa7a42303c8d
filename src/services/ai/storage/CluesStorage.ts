/**
 * ========================================================================
 * CLUES STORAGE
 * ========================================================================
 *
 * Maneja el almacenamiento y gestión de pistas del juego
 * Incluye persistencia en localStorage y métodos de consulta
 * ========================================================================
 */

import type { GameClue, CluesStorage as CluesStorageInterface } from '../models/AIModels';

export class CluesStorage {
  private serviceName = 'cluesStorage';
  private storageKey = 'clues';

  // ========== CARGA Y GUARDADO ==========

  /**
   * Carga las pistas guardadas desde localStorage
   */
  public loadFromStorage(): CluesStorageInterface {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) {
        return { clues: [], lastUpdated: new Date() };
      }

      const parsed = JSON.parse(stored);

      // Convertir timestamps de string a Date si es necesario
      if (parsed.clues) {
        parsed.clues = parsed.clues.map((clue: any) => ({
          ...clue,
          timestamp: clue.timestamp ? new Date(clue.timestamp) : new Date()
        }));
      }

      parsed.lastUpdated = new Date(parsed.lastUpdated);

      return parsed;
    } catch (error) {
      console.warn(`⚠️ [${this.serviceName}] Error cargando pistas desde localStorage:`, error);
      return { clues: [], lastUpdated: new Date() };
    }
  }

  /**
   * Guarda las pistas en localStorage
   */
  public saveToStorage(cluesStorage: CluesStorageInterface): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(cluesStorage));
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error guardando pistas:`, error);
    }
  }

  // ========== GESTIÓN DE PISTAS ==========

  /**
   * Agrega una nueva pista al almacenamiento
   */
  public addClue(clueText: string, sessionId?: string): boolean {
    if (!clueText || clueText.trim().length === 0) {
      console.warn(`⚠️ [${this.serviceName}] Intento de agregar pista vacía`);
      return false;
    }

    const cluesStorage = this.loadFromStorage();

    // Verificar si ya existe una pista similar para evitar duplicados
    const existingClue = cluesStorage.clues.find(clue =>
      clue.text.toLowerCase().trim() === clueText.toLowerCase().trim()
    );

    if (existingClue) {
      return false;
    }

    const newClue: GameClue = {
      id: this.generateClueId(),
      text: clueText.trim(),
      sessionId,
      timestamp: new Date()
    };

    cluesStorage.clues.push(newClue);
    cluesStorage.lastUpdated = new Date();

    // Si hay sessionId, actualizar el sessionId del storage
    if (sessionId) {
      cluesStorage.sessionId = sessionId;
    }

    this.saveToStorage(cluesStorage);
    return true;
  }

  /**
   * Elimina una pista específica por ID
   */
  public removeClue(clueId: string): boolean {
    const cluesStorage = this.loadFromStorage();
    const initialLength = cluesStorage.clues.length;

    cluesStorage.clues = cluesStorage.clues.filter(clue => clue.id !== clueId);

    if (cluesStorage.clues.length < initialLength) {
      cluesStorage.lastUpdated = new Date();
      this.saveToStorage(cluesStorage);
      return true;
    }

    console.warn(`⚠️ [${this.serviceName}] Pista no encontrada para eliminar:`, clueId);
    return false;
  }

  /**
   * Limpia todas las pistas almacenadas
   */
  public clearAll(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error limpiando pistas:`, error);
    }
  }

  /**
   * Limpia las pistas de una sesión específica
   */
  public clearSession(sessionId: string): void {
    const cluesStorage = this.loadFromStorage();
    const initialLength = cluesStorage.clues.length;

    cluesStorage.clues = cluesStorage.clues.filter(clue => clue.sessionId !== sessionId);

    if (cluesStorage.clues.length < initialLength) {
      cluesStorage.lastUpdated = new Date();
      this.saveToStorage(cluesStorage);
    }
  }

  // ========== CONSULTAS ==========

  /**
   * Obtiene todas las pistas almacenadas
   */
  public getAll(): GameClue[] {
    return this.loadFromStorage().clues;
  }

  /**
   * Obtiene las pistas de la sesión actual
   */
  public getBySession(sessionId: string): GameClue[] {
    const cluesStorage = this.loadFromStorage();
    return cluesStorage.clues.filter(clue => clue.sessionId === sessionId);
  }

  /**
   * Obtiene las últimas N pistas
   */
  public getRecent(limit: number = 5): GameClue[] {
    const allClues = this.getAll();
    return allClues
      .sort((a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0))
      .slice(0, limit);
  }

  /**
   * Busca pistas que contengan un texto específico
   */
  public search(searchText: string): GameClue[] {
    const allClues = this.getAll();
    const searchLower = searchText.toLowerCase();

    return allClues.filter(clue =>
      clue.text.toLowerCase().includes(searchLower)
    );
  }

  /**
   * Obtiene estadísticas de las pistas
   */
  public getStats() {
    const allClues = this.getAll();
    const sessions = new Set(allClues.map(clue => clue.sessionId).filter(Boolean));

    return {
      totalClues: allClues.length,
      uniqueSessions: sessions.size,
      oldestClue: allClues.reduce((oldest, clue) => {
        if (!clue.timestamp) return oldest;
        if (!oldest || clue.timestamp < oldest) return clue.timestamp;
        return oldest;
      }, null as Date | null),
      newestClue: allClues.reduce((newest, clue) => {
        if (!clue.timestamp) return newest;
        if (!newest || clue.timestamp > newest) return clue.timestamp;
        return newest;
      }, null as Date | null)
    };
  }

  /**
   * Obtiene el número total de pistas
   */
  public getCount(): number {
    return this.getAll().length;
  }

  // ========== HELPERS ==========

  private generateClueId(): string {
    return `clue-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Verifica si una pista ya existe (similar texto)
   */
  public isDuplicate(clueText: string): boolean {
    const allClues = this.getAll();
    return allClues.some(clue =>
      clue.text.toLowerCase().trim() === clueText.toLowerCase().trim()
    );
  }

  /**
   * Exporta todas las pistas como JSON
   */
  public exportAsJSON(): string {
    const cluesStorage = this.loadFromStorage();
    return JSON.stringify(cluesStorage, null, 2);
  }

  /**
   * Importa pistas desde JSON
   */
  public importFromJSON(jsonString: string): boolean {
    try {
      const imported = JSON.parse(jsonString) as CluesStorageInterface;

      // Validar estructura
      if (!imported.clues || !Array.isArray(imported.clues)) {
        throw new Error('Formato JSON inválido');
      }

      this.saveToStorage(imported);
      return true;
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error importando pistas:`, error);
      return false;
    }
  }
}

// ========== SINGLETON EXPORT ==========
export const cluesStorage = new CluesStorage();
