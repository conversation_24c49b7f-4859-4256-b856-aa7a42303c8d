/**
 * ========================================================================
 * MOVISTAR API SERVICE
 * ========================================================================
 *
 * Servicio para realizar búsquedas de contenido en la API de Movistar Plus
 * Maneja peticiones GET a la API de búsqueda de contenidos
 * ========================================================================
 */

import axios from 'axios';
import { handleRequest } from './_helpersService';
import type { MovistarContent, MovistarSearchOptions } from '../models/services';

// ========== CONFIGURACIÓN ==========
export const MOVISTAR_CONFIG = {
  BASE_URL: import.meta.env.VITE_MOVISTAR_API_URL || 'https://ottcache.dof6.com/movistarplus/homebase/users/contents/search?mode=VODRU7D&profile=anonimo&term=',
  REQUEST_TIMEOUT: 10000, // 10 segundos
  RETRY_ATTEMPTS: 2,
  RETRY_DELAY: 1000, // 1 segundo
} as const;

// ========== TIPOS INTERNOS ==========
interface MovistarAPIResponse {
  // La estructura exacta dependerá de la respuesta real de la API
  // Estos son tipos estimados que pueden necesitar ajuste
  data?: any[];
  results?: any[];
  items?: any[];
  content?: any[];
  [key: string]: any;
}

// ========== CLASE PRINCIPAL ==========
export class MovistarAPIService {
  private serviceName = 'MovistarAPIService';

  // ========== MÉTODO PRINCIPAL DE BÚSQUEDA ==========
  /**
   * Busca contenido por personaje en la API de Movistar Plus
   * @param character Nombre del personaje a buscar
   * @param options Opciones adicionales de búsqueda
   * @returns Primer resultado encontrado o null si no hay resultados
   */
  public async searchByCharacter(
    character: string,
    options: MovistarSearchOptions = {}
  ): Promise<MovistarContent | null> {
    if (!character || character.trim().length === 0) {
      console.warn(`⚠️ [${this.serviceName}] Personaje vacío proporcionado`);
      return null;
    }

    const cleanCharacter = character.trim();
    console.log(`🔍 [${this.serviceName}] Buscando contenido para: "${cleanCharacter}"`);

    try {
      const response = await this.makeSearchRequest(cleanCharacter, options);
      const firstResult = this.extractFirstResult(response);

      if (firstResult) {
        console.log(`✅ [${this.serviceName}] Contenido encontrado:`, firstResult.title);
        return firstResult;
      } else {
        console.log(`ℹ️ [${this.serviceName}] No se encontró contenido para: "${cleanCharacter}"`);
        return null;
      }
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error buscando contenido:`, error);
      return null;
    }
  }

  // ========== MÉTODOS PRIVADOS ==========
  /**
   * Realiza la petición HTTP a la API de Movistar
   */
  private async makeSearchRequest(
    character: string,
    options: MovistarSearchOptions
  ): Promise<MovistarAPIResponse> {
    const url = this.buildSearchURL(character, options);

    console.log(`🌐 [${this.serviceName}] URL de búsqueda: ${url}`);

    const response = await handleRequest<MovistarAPIResponse>(
      axios.get(url, {
        timeout: MOVISTAR_CONFIG.REQUEST_TIMEOUT,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Genigma-WebView/1.0',
        },
      })
    );

    return response;
  }

  /**
   * Construye la URL de búsqueda con los parámetros
   */
  private buildSearchURL(character: string, _options: MovistarSearchOptions): string {
    // La URL base ya incluye los parámetros mode, profile, y term
    // Solo necesitamos añadir el término de búsqueda
    const encodedCharacter = encodeURIComponent(character);

    // Si la URL base ya termina con 'term=', añadimos directamente el personaje
    if (MOVISTAR_CONFIG.BASE_URL.endsWith('term=')) {
      return `${MOVISTAR_CONFIG.BASE_URL}${encodedCharacter}`;
    }

    // Si no, añadimos el parámetro term
    const separator = MOVISTAR_CONFIG.BASE_URL.includes('?') ? '&' : '?';
    return `${MOVISTAR_CONFIG.BASE_URL}${separator}term=${encodedCharacter}`;
  }

  /**
   * Extrae el primer resultado de la respuesta de la API
   */
  private extractFirstResult(response: MovistarAPIResponse): MovistarContent | null {
    // Intentamos diferentes estructuras posibles de respuesta
    const possibleArrays = [
      response.data,
      response.results,
      response.items,
      response.content,
      response.contents,
      response.movies,
      response.series,
    ];

    for (const array of possibleArrays) {
      if (Array.isArray(array) && array.length > 0) {
        return this.mapToMovistarContent(array[0]);
      }
    }

    // Si la respuesta es directamente un array
    if (Array.isArray(response) && response.length > 0) {
      return this.mapToMovistarContent(response[0]);
    }

    return null;
  }

  /**
   * Mapea un objeto de respuesta de la API a MovistarContent
   */
  private mapToMovistarContent(item: any): MovistarContent {
    return {
      id: item.id || item.contentId || item.uuid || String(Math.random()),
      title: item.title || item.name || item.displayName || 'Título desconocido',
      description: item.description || item.synopsis || item.summary,
      image: item.image || item.poster || item.thumbnail || item.imageUrl,
      type: item.type || item.contentType || item.category,
      genre: item.genre || item.genres?.[0],
      year: item.year || item.releaseYear || item.productionYear,
      duration: item.duration || item.runtime,
      rating: item.rating || item.classification,
      url: item.url || item.link || item.deepLink,
    };
  }

  // ========== MÉTODOS DE UTILIDAD ==========
  /**
   * Verifica si el servicio está configurado correctamente
   */
  public isConfigured(): boolean {
    return Boolean(MOVISTAR_CONFIG.BASE_URL);
  }

  /**
   * Obtiene la configuración actual del servicio
   */
  public getConfig() {
    return {
      serviceName: this.serviceName,
      baseUrl: MOVISTAR_CONFIG.BASE_URL,
      timeout: MOVISTAR_CONFIG.REQUEST_TIMEOUT,
      retryAttempts: MOVISTAR_CONFIG.RETRY_ATTEMPTS,
    };
  }

  /**
   * Realiza una búsqueda de prueba para verificar conectividad
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Usamos un término de búsqueda genérico para la prueba
      await this.searchByCharacter('test');
      return true;
    } catch {
      return false;
    }
  }
}

// ========== SINGLETON EXPORT ==========
export const movistarAPIService = new MovistarAPIService();
