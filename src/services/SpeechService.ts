// Exportar el servicio principal
export { SpeechService } from "./speech/speechService";
import { SpeechService } from "./speech/speechService";

// Exportar servicios específicos
export { AzureVoicesService } from "./speech/azureVoicesService";

// Exportar utilidades
export { AudioCache } from "./speech/cache";
export { RetryQueue } from "./speech/retryQueue";
export * from "./speech/textUtils";

// Exportar configuración
export * from "./speech/config";

// Exportar interfaces
export * from "../models/speech";

// Singleton
export const speechService = new SpeechService();

// Hacer disponible globalmente para debugging
if (typeof window !== 'undefined') {
  (window as any).speechService = speechService;
}

