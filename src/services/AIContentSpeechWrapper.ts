/**
 * ========================================================================
 * AI CONTENT SPEECH WRAPPER
 * ========================================================================
 *
 * Centralized wrapper that ensures all AI-generated content is automatically
 * narrated through the Azure TTS system while respecting mute/unmute controls.
 *
 * Features:
 * - Automatic speech for all AI responses
 * - Respects speech coordinator settings
 * - Prevents duplicate narration
 * - Configurable speech types for different content
 * ========================================================================
 */

import { speechCoordinator } from "./SpeechCoordinator";
import { aiService } from "./AIService";
import { characterEnhancementService } from "./CharacterEnhancementService";
import type { AIResponse } from "../models/services";
import type { GameMode } from "../contexts/EnygmaGameContext";
import type { EnhancedCharacter } from "./CharacterEnhancementService";

// ========== CONFIGURATION ==========
interface SpeechConfig {
  enabled: boolean;
  characterAnnouncement: boolean;
  responseNarration: boolean;
  welcomeMessages: boolean;
}

// ========== AI CONTENT SPEECH WRAPPER ==========
class AIContentSpeechWrapper {
  private static instance: AIContentSpeechWrapper;
  // private serviceName = "aiContentSpeech";

  private config: SpeechConfig = {
    enabled: true,
    characterAnnouncement: true,
    responseNarration: true,
    welcomeMessages: true,
  };

  private constructor() {}

  public static getInstance(): AIContentSpeechWrapper {
    if (!AIContentSpeechWrapper.instance) {
      AIContentSpeechWrapper.instance = new AIContentSpeechWrapper();
    }
    return AIContentSpeechWrapper.instance;
  }

  // ========== CONFIGURATION METHODS ==========
  public enableAllSpeech(): void {
    this.config.enabled = true;
    aiService.enableAutoSpeech();
  }

  public disableAllSpeech(): void {
    this.config.enabled = false;
    aiService.disableAutoSpeech();
  }

  public enableCharacterAnnouncements(): void {
    this.config.characterAnnouncement = true;
  }

  public disableCharacterAnnouncements(): void {
    this.config.characterAnnouncement = false;
  }

  public enableResponseNarration(): void {
    this.config.responseNarration = true;
  }

  public disableResponseNarration(): void {
    this.config.responseNarration = false;
  }

  // ========== WRAPPER METHODS ==========
  /**
   * Generate AI response with automatic speech narration
   */
  public async generateResponseWithSpeech(
    query: string,
    mode: GameMode,
    character?: string
  ): Promise<AIResponse> {
    if (!this.config.enabled || !this.config.responseNarration) {
      // Use silent version if speech is disabled
      return aiService.generateResponseSilent(query, mode, character);
    }

    // Use normal version with automatic speech
    return aiService.generateResponse(query, mode, character);
  }

  /**
   * Generate character with automatic announcement
   */
  public async generateCharacterWithAnnouncement(): Promise<string | null> {
    if (!this.config.enabled || !this.config.characterAnnouncement) {
      // Temporarily disable auto-speech for character generation
      const wasEnabled = aiService.isAutoSpeechEnabled();
      aiService.disableAutoSpeech();

      try {
        return await aiService.generateCharacter();
      } finally {
        if (wasEnabled) {
          aiService.enableAutoSpeech();
        }
      }
    }

    // Use normal version with automatic speech
    return aiService.generateCharacter();
  }

  /**
   * Generate and enhance character with Movistar Plus content
   */
  public async generateEnhancedCharacter(): Promise<EnhancedCharacter | null> {
    try {
      // First, generate a character using AI
      const characterName = await this.generateCharacterWithAnnouncement();

      if (!characterName) {
        console.warn('⚠️ [AIContentSpeechWrapper] No se pudo generar personaje');
        return null;
      }

      // Then, enhance it with Movistar Plus content
      console.log(`🔍 [AIContentSpeechWrapper] Mejorando personaje: "${characterName}"`);
      const enhancedCharacter = await characterEnhancementService.enhanceCharacter(characterName, {
        enableMovistarSearch: true,
        maxRetries: 2,
        fallbackToGeneric: true,
        generateHints: true,
      });

      // Announce enhancement result if speech is enabled
      if (this.config.enabled && this.config.characterAnnouncement) {
        await this.announceCharacterEnhancement(enhancedCharacter);
      }

      return enhancedCharacter;
    } catch (error) {
      console.error('❌ [AIContentSpeechWrapper] Error generando personaje mejorado:', error);
      return null;
    }
  }

  /**
   * Announce character enhancement result
   */
  private async announceCharacterEnhancement(character: EnhancedCharacter): Promise<void> {
    try {
      let announcement = `He seleccionado a ${character.name}.`;

      if (character.enhancementStatus === 'enhanced' && character.movistarContent) {
        announcement += ` Encontré información relacionada en Movistar Plus.`;
      } else if (character.enhancementStatus === 'not_found') {
        announcement += ` Listo para empezar el juego.`;
      }

      await speechCoordinator.speak(announcement, {
        type: 'character',
        priority: 'medium',
        channel: 'main'
      });
    } catch (error) {
      console.warn('⚠️ [AIContentSpeechWrapper] Error anunciando mejora de personaje:', error);
    }
  }

  /**
   * Start new game with appropriate speech settings
   */
  public async startNewGameWithSpeech(
    mode: GameMode,
    character?: string
  ): Promise<AIResponse | null> {
    if (!this.config.enabled || !this.config.welcomeMessages) {
      // Use silent version if welcome messages are disabled
      const wasEnabled = aiService.isAutoSpeechEnabled();
      aiService.disableAutoSpeech();

      try {
        return await aiService.startNewGame(mode, character);
      } finally {
        if (wasEnabled) {
          aiService.enableAutoSpeech();
        }
      }
    }

    // Use normal version with automatic speech
    return aiService.startNewGame(mode, character);
  }

  // ========== UTILITY METHODS ==========
  /**
   * Check if speech is currently enabled
   */
  public isSpeechEnabled(): boolean {
    return this.config.enabled && speechCoordinator.getState().isPlaying !== undefined;
  }

  /**
   * Get current configuration
   */
  public getConfig(): SpeechConfig {
    return { ...this.config };
  }

  /**
   * Reset all configurations to default
   */
  public resetToDefaults(): void {
    this.config = {
      enabled: true,
      characterAnnouncement: true,
      responseNarration: true,
      welcomeMessages: true,
    };
    aiService.enableAutoSpeech();
  }
}

// ========== SINGLETON EXPORT ==========
export const aiContentSpeechWrapper = AIContentSpeechWrapper.getInstance();
