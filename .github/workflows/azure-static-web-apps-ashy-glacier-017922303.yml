name: Azure Static Web Apps CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    types: [ opened, synchronize, reopened ]
    branches: [ main ]

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:

      # 1) Checkout repo
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # 2) Setup SSH access to GitHub
      - name: Setup SSH access to GitHub
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          ssh-keyscan github.com >> ~/.ssh/known_hosts

      # 3) Setup Node.js 20.x
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      # 4) Install dependencies
      - name: Install dependencies
        run: npm ci

      # 5) Build your app
      - name: Build
        run: npm run build

      # 6) List files
      - name: List dist folder files
        run: ls -lR dist

      # 7) Deploy to Azure Static Web Apps
      - name: Deploy to Azure Static Web Apps
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_ASHY_GLACIER_017922303 }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: upload
          app_location: '/'
          api_location: 'swa-db-connections'
          output_location: 'dist'
