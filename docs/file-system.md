.
├── docs
│   ├── diseño
│   │   ├── 01.png
│   │   ├── 02.png
│   │   ├── 03.png
│   │   ├── 04.png
│   │   ├── 05.png
│   │   ├── 06.png
│   │   ├── 07.png
│   │   ├── 08.png
│   │   ├── 09.png
│   │   └── 10.png
│   ├── figma.md
│   └── file-system.md
├── .env
├── eslint.config.js
├── .github
│   └── workflows
│       └── azure-static-web-apps-ashy-glacier-017922303.yml
├── .gitignore
├── index.html
├── package.json
├── package-lock.json
├── public
│   ├── assets
│   │   ├── favicon.png
│   │   ├── fonts
│   │   │   ├── OnAir-BlackItalic.ttf
│   │   │   ├── OnAir-Black.ttf
│   │   │   ├── OnAir-BoldItalic.ttf
│   │   │   ├── OnAir-Bold.ttf
│   │   │   ├── OnAir-Italic.ttf
│   │   │   ├── OnAir-LightItalic.ttf
│   │   │   ├── OnAir-Light.ttf
│   │   │   ├── OnAirOutlineOne.ttf
│   │   │   ├── OnAirOutlineThree.ttf
│   │   │   ├── OnAirOutlineTwo.ttf
│   │   │   └── OnAir-Regular.ttf
│   │   ├── game
│   │   │   ├── background.png
│   │   │   ├── book_1.png
│   │   │   ├── book_2.png
│   │   │   ├── book.png
│   │   │   ├── clues.png
│   │   │   ├── enygma.png
│   │   │   ├── exit.png
│   │   │   ├── lives.png
│   │   │   └── player.png
│   │   └── sounds
│   │       ├── sound_1.mp3
│   │       ├── sound_3.mp3
│   │       └── sound.mp3
│   ├── azure-voice-conf.json
│   ├── game-modes.json
│   └── game-rules.json
├── README.md
├── src
│   ├── animations.scss
│   ├── App.scss
│   ├── App.tsx
│   ├── components
│   │   ├── Auxiliar
│   │   │   └── CookieConsentBanner.tsx
│   │   ├── Buttons
│   │   │   ├── Aura
│   │   │   │   └── IconAura.tsx
│   │   │   ├── GoBack
│   │   │   │   └── IconGoBack.tsx
│   │   │   ├── Menu
│   │   │   │   └── IconMenu.tsx
│   │   │   ├── MicrophoneOff
│   │   │   │   └── IconMicrophoneOff.tsx
│   │   │   ├── MicrophoneOn
│   │   │   │   └── IconMicrophoneOn.tsx
│   │   │   ├── Next
│   │   │   │   └── IconNext.tsx
│   │   │   ├── Prev
│   │   │   │   └── IconPrev.tsx
│   │   │   ├── SoundOff
│   │   │   │   └── IconSoundOff.tsx
│   │   │   └── SoundOn
│   │   │       └── IconSoundOn.tsx
│   │   ├── Header
│   │   │   ├── Header.scss
│   │   │   └── Header.tsx
│   │   └── Views
│   │       ├── CluesView
│   │       │   ├── CluesView.scss
│   │       │   └── CluesView.tsx
│   │       ├── MainView
│   │       │   ├── MainView.scss
│   │       │   └── MainView.tsx
│   │       ├── PlayView
│   │       │   ├── GameResultView.scss
│   │       │   ├── GameResultView.tsx
│   │       │   ├── PlayView.scss
│   │       │   └── PlayView.tsx
│   │       ├── RulesView
│   │       │   ├── RulesView.scss
│   │       │   └── RulesView.tsx
│   │       └── WelcomeScreen
│   │           ├── WelcomeScreen.scss
│   │           └── WelcomeScreen.tsx
│   ├── contexts
│   │   ├── AppContext.tsx
│   │   ├── EnygmaGameContext.tsx
│   │   ├── EventBusContext.tsx
│   │   ├── GameOrchestratorContext.tsx
│   │   └── SpeechProvider.tsx
│   ├── hooks
│   │   ├── useGameResponseValidator.ts
│   │   └── useSpeechCoordinator.ts
│   ├── index.scss
│   ├── main.tsx
│   ├── models
│   │   ├── app.ts
│   │   ├── audio.ts
│   │   ├── components.ts
│   │   ├── config.ts
│   │   ├── contexts.ts
│   │   ├── game.ts
│   │   ├── index.ts
│   │   ├── services.ts
│   │   └── speech.ts
│   ├── services
│   │   ├── ai
│   │   │   ├── config
│   │   │   │   └── APIConfig.ts
│   │   │   ├── models
│   │   │   │   ├── AIModels.ts
│   │   │   │   └── ResponseParser.ts
│   │   │   ├── speech
│   │   │   │   └── SpeechIntegration.ts
│   │   │   ├── storage
│   │   │   │   ├── CharacterStorage.ts
│   │   │   │   └── CluesStorage.ts
│   │   │   └── utils
│   │   │       ├── PayloadBuilder.ts
│   │   │       └── RequestHelper.ts
│   │   ├── AIContentSpeechWrapper.ts
│   │   ├── AIService.ts
│   │   ├── AudioManager.ts
│   │   ├── GameResponseValidationService.ts
│   │   ├── _helpersService.ts
│   │   ├── log
│   │   │   ├── ConsoleInterceptor.ts
│   │   │   ├── ConsoleTransport.ts
│   │   │   ├── LogFilterer.ts
│   │   │   ├── LogService.ts
│   │   │   ├── LogTypes.ts
│   │   │   └── RawConsole.ts
│   │   ├── LogService.ts
│   │   ├── speech
│   │   │   ├── azureVoicesService.ts
│   │   │   ├── cache.ts
│   │   │   ├── config.ts
│   │   │   ├── retryQueue.ts
│   │   │   ├── speechService.ts
│   │   │   └── textUtils.ts
│   │   ├── SpeechCoordinator.ts
│   │   ├── SpeechService.ts
│   │   └── TextNormalizationService.ts
│   ├── utils
│   │   └── questionsColorSystem.ts
│   └── vite-env.d.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts

43 directories, 127 files
